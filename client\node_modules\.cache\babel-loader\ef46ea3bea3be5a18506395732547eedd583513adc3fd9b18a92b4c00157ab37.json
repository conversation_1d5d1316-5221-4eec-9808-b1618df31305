{"ast": null, "code": "function curry(fn) {\n  return function curried() {\n    var _this = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\nexport default curry;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}