{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"14\",\n  y: \"14\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1b0bso\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"2\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1x09vl\"\n}], [\"path\", {\n  d: \"M7 14v1a2 2 0 0 0 2 2h1\",\n  key: \"pao6x6\"\n}], [\"path\", {\n  d: \"M14 7h1a2 2 0 0 1 2 2v1\",\n  key: \"19tdru\"\n}]];\nconst SendToBack = createLucideIcon(\"send-to-back\", __iconNode);\nexport { __iconNode, SendToBack as default };\n//# sourceMappingURL=send-to-back.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}