{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 12h2v8\",\n  key: \"c1fccl\"\n}], [\"path\", {\n  d: \"M14 20h4\",\n  key: \"lzx1xo\"\n}], [\"path\", {\n  d: \"M6 12h4\",\n  key: \"a4o3ry\"\n}], [\"path\", {\n  d: \"M6 20h4\",\n  key: \"1i6q5t\"\n}], [\"path\", {\n  d: \"M8 20V8a4 4 0 0 1 7.464-2\",\n  key: \"wk9t6r\"\n}]];\nconst Ligature = createLucideIcon(\"ligature\", __iconNode);\nexport { __iconNode, Ligature as default };\n//# sourceMappingURL=ligature.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}