{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 13V19H13\",\n  key: \"10vkzq\"\n}], [\"path\", {\n  d: \"M5 5L19 19\",\n  key: \"5zm2fv\"\n}]];\nconst MoveDownRight = createLucideIcon(\"move-down-right\", __iconNode);\nexport { __iconNode, MoveDownRight as default };\n//# sourceMappingURL=move-down-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}