{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12V8.964\",\n  key: \"1vll13\"\n}], [\"path\", {\n  d: \"M14 12V8.964\",\n  key: \"1x3qvg\"\n}], [\"path\", {\n  d: \"M15 12a1 1 0 0 1 1 1v2a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2v-2a1 1 0 0 1 1-1z\",\n  key: \"ppykja\"\n}], [\"path\", {\n  d: \"M8.5 21H5a2 2 0 0 1-2-2v-9a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2h-5a2 2 0 0 1-2-2v-2\",\n  key: \"1gvg2z\"\n}]];\nconst HousePlug = createLucideIcon(\"house-plug\", __iconNode);\nexport { __iconNode, HousePlug as default };\n//# sourceMappingURL=house-plug.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}