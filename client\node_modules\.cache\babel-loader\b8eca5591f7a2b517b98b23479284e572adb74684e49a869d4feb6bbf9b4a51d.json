{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"19\",\n  x2: \"10\",\n  y1: \"4\",\n  y2: \"4\",\n  key: \"15jd3p\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"5\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"bu0au3\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"9\",\n  y1: \"4\",\n  y2: \"20\",\n  key: \"uljnxc\"\n}]];\nconst Italic = createLucideIcon(\"italic\", __iconNode);\nexport { __iconNode, Italic as default };\n//# sourceMappingURL=italic.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}