{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2\",\n  key: \"4125el\"\n}], [\"path\", {\n  d: \"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21\",\n  key: \"1dpki6\"\n}]];\nconst WalletCards = createLucideIcon(\"wallet-cards\", __iconNode);\nexport { __iconNode, WalletCards as default };\n//# sourceMappingURL=wallet-cards.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}