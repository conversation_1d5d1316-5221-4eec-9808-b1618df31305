{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"16\",\n  y: \"16\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"4q2zg0\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"16\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"8cvhb9\"\n}], [\"rect\", {\n  x: \"9\",\n  y: \"2\",\n  width: \"6\",\n  height: \"6\",\n  rx: \"1\",\n  key: \"1egb70\"\n}], [\"path\", {\n  d: \"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3\",\n  key: \"1jsf9p\"\n}], [\"path\", {\n  d: \"M12 12V8\",\n  key: \"2874zd\"\n}]];\nconst Network = createLucideIcon(\"network\", __iconNode);\nexport { __iconNode, Network as default };\n//# sourceMappingURL=network.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}