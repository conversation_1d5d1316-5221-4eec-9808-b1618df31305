{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 2 4 4-4 4\",\n  key: \"nntrym\"\n}], [\"path\", {\n  d: \"M3 11v-1a4 4 0 0 1 4-4h14\",\n  key: \"84bu3i\"\n}], [\"path\", {\n  d: \"m7 22-4-4 4-4\",\n  key: \"1wqhfi\"\n}], [\"path\", {\n  d: \"M21 13v1a4 4 0 0 1-4 4H3\",\n  key: \"1rx37r\"\n}], [\"path\", {\n  d: \"M11 10h1v4\",\n  key: \"70cz1p\"\n}]];\nconst Repeat1 = createLucideIcon(\"repeat-1\", __iconNode);\nexport { __iconNode, Repeat1 as default };\n//# sourceMappingURL=repeat-1.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}