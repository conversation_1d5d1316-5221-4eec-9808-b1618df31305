{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v2\",\n  key: \"j91f56\"\n}], [\"path\", {\n  d: \"M20 12v2\",\n  key: \"w8o0tu\"\n}], [\"path\", {\n  d: \"M20 18v2a2 2 0 0 1-2 2h-1\",\n  key: \"1c9ggx\"\n}], [\"path\", {\n  d: \"M13 22h-2\",\n  key: \"191ugt\"\n}], [\"path\", {\n  d: \"M7 22H6a2 2 0 0 1-2-2v-2\",\n  key: \"1rt9px\"\n}], [\"path\", {\n  d: \"M4 14v-2\",\n  key: \"1v0sqh\"\n}], [\"path\", {\n  d: \"M4 8V6a2 2 0 0 1 2-2h2\",\n  key: \"1mwabg\"\n}], [\"path\", {\n  d: \"M8 10h6\",\n  key: \"3oa6kw\"\n}], [\"path\", {\n  d: \"M8 14h8\",\n  key: \"1fgep2\"\n}], [\"path\", {\n  d: \"M8 18h5\",\n  key: \"17enja\"\n}]];\nconst NotepadTextDashed = createLucideIcon(\"notepad-text-dashed\", __iconNode);\nexport { __iconNode, NotepadTextDashed as default };\n//# sourceMappingURL=notepad-text-dashed.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}