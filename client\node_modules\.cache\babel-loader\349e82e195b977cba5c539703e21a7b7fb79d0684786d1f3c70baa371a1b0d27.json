{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 13v8\",\n  key: \"1l5pq0\"\n}], [\"path\", {\n  d: \"M12 3v3\",\n  key: \"1n5kay\"\n}], [\"path\", {\n  d: \"M18 6a2 2 0 0 1 1.387.56l2.307 2.22a1 1 0 0 1 0 1.44l-2.307 2.22A2 2 0 0 1 18 13H6a2 2 0 0 1-1.387-.56l-2.306-2.22a1 1 0 0 1 0-1.44l2.306-2.22A2 2 0 0 1 6 6z\",\n  key: \"gqqp9m\"\n}]];\nconst Signpost = createLucideIcon(\"signpost\", __iconNode);\nexport { __iconNode, Signpost as default };\n//# sourceMappingURL=signpost.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}