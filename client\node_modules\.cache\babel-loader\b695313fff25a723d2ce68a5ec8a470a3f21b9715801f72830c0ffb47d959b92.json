{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\",\n  key: \"wrbu53\"\n}], [\"path\", {\n  d: \"M15 18H9\",\n  key: \"1lyqi6\"\n}], [\"path\", {\n  d: \"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14\",\n  key: \"lysw3i\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"332jqn\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}]];\nconst Truck = createLucideIcon(\"truck\", __iconNode);\nexport { __iconNode, Truck as default };\n//# sourceMappingURL=truck.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}