{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 7V5a2 2 0 0 1 2-2h2\",\n  key: \"aa7l1z\"\n}], [\"path\", {\n  d: \"M17 3h2a2 2 0 0 1 2 2v2\",\n  key: \"4qcy5o\"\n}], [\"path\", {\n  d: \"M21 17v2a2 2 0 0 1-2 2h-2\",\n  key: \"6vwrx8\"\n}], [\"path\", {\n  d: \"M7 21H5a2 2 0 0 1-2-2v-2\",\n  key: \"ioqczr\"\n}], [\"path\", {\n  d: \"M8 7v10\",\n  key: \"23sfjj\"\n}], [\"path\", {\n  d: \"M12 7v10\",\n  key: \"jspqdw\"\n}], [\"path\", {\n  d: \"M17 7v10\",\n  key: \"578dap\"\n}]];\nconst ScanBarcode = createLucideIcon(\"scan-barcode\", __iconNode);\nexport { __iconNode, ScanBarcode as default };\n//# sourceMappingURL=scan-barcode.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}