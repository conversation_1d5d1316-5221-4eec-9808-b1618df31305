{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 7h6v6\",\n  key: \"box55l\"\n}], [\"path\", {\n  d: \"m22 7-8.5 8.5-5-5L2 17\",\n  key: \"1t1m79\"\n}]];\nconst TrendingUp = createLucideIcon(\"trending-up\", __iconNode);\nexport { __iconNode, TrendingUp as default };\n//# sourceMappingURL=trending-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}