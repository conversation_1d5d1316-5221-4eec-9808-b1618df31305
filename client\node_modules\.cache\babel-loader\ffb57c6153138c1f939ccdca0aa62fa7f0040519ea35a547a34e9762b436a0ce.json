{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21\",\n  key: \"9csbqa\"\n}], [\"path\", {\n  d: \"m14 19 3 3v-5.5\",\n  key: \"9ldu5r\"\n}], [\"path\", {\n  d: \"m17 22 3-3\",\n  key: \"1nkfve\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}]];\nconst ImageDown = createLucideIcon(\"image-down\", __iconNode);\nexport { __iconNode, ImageDown as default };\n//# sourceMappingURL=image-down.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}