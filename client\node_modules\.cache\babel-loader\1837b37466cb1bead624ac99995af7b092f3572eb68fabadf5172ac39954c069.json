{"ast": null, "code": "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'];\nexport function parse(str) {\n  if (str.length > 8000) {\n    throw \"URI too long\";\n  }\n  const src = str,\n    b = str.indexOf('['),\n    e = str.indexOf(']');\n  if (b != -1 && e != -1) {\n    str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n  }\n  let m = re.exec(str || ''),\n    uri = {},\n    i = 14;\n  while (i--) {\n    uri[parts[i]] = m[i] || '';\n  }\n  if (b != -1 && e != -1) {\n    uri.source = src;\n    uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n    uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n    uri.ipv6uri = true;\n  }\n  uri.pathNames = pathNames(uri, uri['path']);\n  uri.queryKey = queryKey(uri, uri['query']);\n  return uri;\n}\nfunction pathNames(obj, path) {\n  const regx = /\\/{2,9}/g,\n    names = path.replace(regx, \"/\").split(\"/\");\n  if (path.slice(0, 1) == '/' || path.length === 0) {\n    names.splice(0, 1);\n  }\n  if (path.slice(-1) == '/') {\n    names.splice(names.length - 1, 1);\n  }\n  return names;\n}\nfunction queryKey(uri, query) {\n  const data = {};\n  query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n    if ($1) {\n      data[$1] = $2;\n    }\n  });\n  return data;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}