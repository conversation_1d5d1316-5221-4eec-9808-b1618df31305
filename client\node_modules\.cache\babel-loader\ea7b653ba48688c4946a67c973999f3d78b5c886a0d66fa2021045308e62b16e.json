{"ast": null, "code": "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n  return config;\n}\n/**\n * logs deprecation message\n */\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\nexport default validators;\nexport { errorHandler, errorMessages };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}