{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0\",\n  key: \"vqan6v\"\n}], [\"path\", {\n  d: \"M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0\",\n  key: \"wdjd8o\"\n}]];\nconst Section = createLucideIcon(\"section\", __iconNode);\nexport { __iconNode, Section as default };\n//# sourceMappingURL=section.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}