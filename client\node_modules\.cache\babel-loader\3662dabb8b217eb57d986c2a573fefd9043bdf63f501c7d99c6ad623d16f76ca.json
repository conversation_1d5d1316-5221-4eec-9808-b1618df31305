{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 22v-4a2 2 0 1 0-4 0v4\",\n  key: \"hhkicm\"\n}], [\"path\", {\n  d: \"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10\",\n  key: \"1xqip1\"\n}], [\"path\", {\n  d: \"M18 5v17\",\n  key: \"1sw6gf\"\n}], [\"path\", {\n  d: \"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6\",\n  key: \"9d2mlk\"\n}], [\"path\", {\n  d: \"M6 5v17\",\n  key: \"1xfsm0\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"1092wv\"\n}]];\nconst School = createLucideIcon(\"school\", __iconNode);\nexport { __iconNode, School as default };\n//# sourceMappingURL=school.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}