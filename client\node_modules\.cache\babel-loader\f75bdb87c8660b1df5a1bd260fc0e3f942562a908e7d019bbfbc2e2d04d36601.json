{"ast": null, "code": "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeFile = typeof File === \"function\" || typeof File !== \"undefined\" && toString.call(File) === \"[object FileConstructor]\";\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n  return withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)) || withNativeBlob && obj instanceof Blob || withNativeFile && obj instanceof File;\n}\nexport function hasBinary(obj, toJSON) {\n  if (!obj || typeof obj !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(obj)) {\n    for (let i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n  if (isBinary(obj)) {\n    return true;\n  }\n  if (obj.toJSON && typeof obj.toJSON === \"function\" && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n  return false;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}