{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.034 2.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.944L8.204 7.545a1 1 0 0 0-.66.66l-1.066 3.443a.5.5 0 0 1-.944.033z\",\n  key: \"11pp1i\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"16\",\n  r: \"6\",\n  key: \"qoo3c4\"\n}], [\"path\", {\n  d: \"m11.8 11.8 8.4 8.4\",\n  key: \"oogvdj\"\n}]];\nconst MousePointerBan = createLucideIcon(\"mouse-pointer-ban\", __iconNode);\nexport { __iconNode, MousePointerBan as default };\n//# sourceMappingURL=mouse-pointer-ban.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}