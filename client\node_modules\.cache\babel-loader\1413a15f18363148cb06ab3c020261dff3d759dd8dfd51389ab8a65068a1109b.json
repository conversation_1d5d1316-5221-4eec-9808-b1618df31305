{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polyline\", {\n  points: \"3.5 2 6.5 12.5 18 12.5\",\n  key: \"y3iy52\"\n}], [\"line\", {\n  x1: \"9.5\",\n  x2: \"5.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"19vg5i\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"18.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"1inpmv\"\n}], [\"path\", {\n  d: \"M2.75 18a13 13 0 0 0 18.5 0\",\n  key: \"1nquas\"\n}]];\nconst RockingChair = createLucideIcon(\"rocking-chair\", __iconNode);\nexport { __iconNode, RockingChair as default };\n//# sourceMappingURL=rocking-chair.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}