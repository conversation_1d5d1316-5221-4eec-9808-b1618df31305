{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18.6 14.4c.8-.8.8-2 0-2.8l-8.1-8.1a4.95 4.95 0 1 0-7.1 7.1l8.1 8.1c.9.7 2.1.7 2.9-.1Z\",\n  key: \"1o68ps\"\n}], [\"path\", {\n  d: \"m22 22-5.5-5.5\",\n  key: \"17o70y\"\n}]];\nconst Popsicle = createLucideIcon(\"popsicle\", __iconNode);\nexport { __iconNode, Popsicle as default };\n//# sourceMappingURL=popsicle.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}