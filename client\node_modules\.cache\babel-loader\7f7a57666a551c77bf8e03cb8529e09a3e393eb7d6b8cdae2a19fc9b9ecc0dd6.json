{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}], [\"path\", {\n  d: \"M10 12H3\",\n  key: \"1ulcyk\"\n}], [\"path\", {\n  d: \"M10 18H3\",\n  key: \"13769t\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"15\",\n  r: \"3\",\n  key: \"1upz2a\"\n}], [\"path\", {\n  d: \"m21 19-1.9-1.9\",\n  key: \"dwi7p8\"\n}]];\nconst TextSearch = createLucideIcon(\"text-search\", __iconNode);\nexport { __iconNode, TextSearch as default };\n//# sourceMappingURL=text-search.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}