{"ast": null, "code": "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\nexport default merge;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}