{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v6\",\n  key: \"1holv5\"\n}], [\"path\", {\n  d: \"M16.76 3a2 2 0 0 1 1.8 1.1l2.23 4.479a2 2 0 0 1 .21.891V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9.472a2 2 0 0 1 .211-.894L5.45 4.1A2 2 0 0 1 7.24 3z\",\n  key: \"187q7i\"\n}], [\"path\", {\n  d: \"M3.054 9.013h17.893\",\n  key: \"grwhos\"\n}]];\nconst Package2 = createLucideIcon(\"package-2\", __iconNode);\nexport { __iconNode, Package2 as default };\n//# sourceMappingURL=package-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}