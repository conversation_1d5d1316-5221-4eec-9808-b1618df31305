{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20.341 6.484A10 10 0 0 1 10.266 21.85\",\n  key: \"1enhxb\"\n}], [\"path\", {\n  d: \"M3.659 17.516A10 10 0 0 1 13.74 2.152\",\n  key: \"1crzgf\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"mhkx31\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"v8kfzx\"\n}]];\nconst Orbit = createLucideIcon(\"orbit\", __iconNode);\nexport { __iconNode, Orbit as default };\n//# sourceMappingURL=orbit.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}