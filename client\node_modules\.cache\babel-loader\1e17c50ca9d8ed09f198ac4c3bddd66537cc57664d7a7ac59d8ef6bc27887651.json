{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 19H5V13\",\n  key: \"1akmht\"\n}], [\"path\", {\n  d: \"M19 5L5 19\",\n  key: \"72u4yj\"\n}]];\nconst MoveDownLeft = createLucideIcon(\"move-down-left\", __iconNode);\nexport { __iconNode, MoveDownLeft as default };\n//# sourceMappingURL=move-down-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}