{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"rect\", {\n  width: \"3\",\n  height: \"9\",\n  x: \"7\",\n  y: \"7\",\n  key: \"14n3xi\"\n}], [\"rect\", {\n  width: \"3\",\n  height: \"5\",\n  x: \"14\",\n  y: \"7\",\n  key: \"s4azjd\"\n}]];\nconst Trello = createLucideIcon(\"trello\", __iconNode);\nexport { __iconNode, Trello as default };\n//# sourceMappingURL=trello.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}