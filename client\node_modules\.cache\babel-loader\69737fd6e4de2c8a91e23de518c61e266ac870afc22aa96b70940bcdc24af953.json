{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 14 3 3.3a1 1 0 0 1-.7 1.7H4.7a1 1 0 0 1-.7-1.7L7 14h-.3a1 1 0 0 1-.7-1.7L9 9h-.2A1 1 0 0 1 8 7.3L12 3l4 4.3a1 1 0 0 1-.8 1.7H15l3 3.3a1 1 0 0 1-.7 1.7H17Z\",\n  key: \"cpyugq\"\n}], [\"path\", {\n  d: \"M12 22v-3\",\n  key: \"kmzjlo\"\n}]];\nconst TreePine = createLucideIcon(\"tree-pine\", __iconNode);\nexport { __iconNode, TreePine as default };\n//# sourceMappingURL=tree-pine.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}