{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-2\",\n  key: \"4b9dqc\"\n}], [\"path\", {\n  d: \"M6 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-2\",\n  key: \"22nnkd\"\n}], [\"path\", {\n  d: \"M6 6h.01\",\n  key: \"1utrut\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"m13 6-4 6h6l-4 6\",\n  key: \"14hqih\"\n}]];\nconst ServerCrash = createLucideIcon(\"server-crash\", __iconNode);\nexport { __iconNode, ServerCrash as default };\n//# sourceMappingURL=server-crash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}