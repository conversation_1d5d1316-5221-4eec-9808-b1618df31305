{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 10h2\",\n  key: \"1lstlu\"\n}], [\"path\", {\n  d: \"M15 22v-8\",\n  key: \"1fwwgm\"\n}], [\"path\", {\n  d: \"M15 2v4\",\n  key: \"1044rn\"\n}], [\"path\", {\n  d: \"M2 10h2\",\n  key: \"1r8dkt\"\n}], [\"path\", {\n  d: \"M20 10h2\",\n  key: \"1ug425\"\n}], [\"path\", {\n  d: \"M3 19h18\",\n  key: \"awlh7x\"\n}], [\"path\", {\n  d: \"M3 22v-6a2 2 135 0 1 2-2h14a2 2 45 0 1 2 2v6\",\n  key: \"ibqhof\"\n}], [\"path\", {\n  d: \"M3 2v2a2 2 45 0 0 2 2h14a2 2 135 0 0 2-2V2\",\n  key: \"1uenja\"\n}], [\"path\", {\n  d: \"M8 10h2\",\n  key: \"66od0\"\n}], [\"path\", {\n  d: \"M9 22v-8\",\n  key: \"fmnu31\"\n}], [\"path\", {\n  d: \"M9 2v4\",\n  key: \"j1yeou\"\n}]];\nconst TableRowsSplit = createLucideIcon(\"table-rows-split\", __iconNode);\nexport { __iconNode, TableRowsSplit as default };\n//# sourceMappingURL=table-rows-split.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}