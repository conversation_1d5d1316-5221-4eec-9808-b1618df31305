{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 16v6\",\n  key: \"c8a4gj\"\n}], [\"path\", {\n  d: \"M14 20h-4\",\n  key: \"m8m19d\"\n}], [\"path\", {\n  d: \"M18 2h4v4\",\n  key: \"1341mj\"\n}], [\"path\", {\n  d: \"m2 2 7.17 7.17\",\n  key: \"13q8l2\"\n}], [\"path\", {\n  d: \"M2 5.355V2h3.357\",\n  key: \"18136r\"\n}], [\"path\", {\n  d: \"m22 2-7.17 7.17\",\n  key: \"1epvy4\"\n}], [\"path\", {\n  d: \"M8 5 5 8\",\n  key: \"mgbjhz\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}]];\nconst Transgender = createLucideIcon(\"transgender\", __iconNode);\nexport { __iconNode, Transgender as default };\n//# sourceMappingURL=transgender.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}