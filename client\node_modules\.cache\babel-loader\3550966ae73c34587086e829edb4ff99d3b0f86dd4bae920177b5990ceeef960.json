{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 3v11\",\n  key: \"mlfb7b\"\n}], [\"path\", {\n  d: \"M14 9h-3a3 3 0 0 1 0-6h9\",\n  key: \"1ulc19\"\n}], [\"path\", {\n  d: \"M18 3v11\",\n  key: \"1phi0r\"\n}], [\"path\", {\n  d: \"M22 18H2l4-4\",\n  key: \"yt65j9\"\n}], [\"path\", {\n  d: \"m6 22-4-4\",\n  key: \"6jgyf5\"\n}]];\nconst PilcrowLeft = createLucideIcon(\"pilcrow-left\", __iconNode);\nexport { __iconNode, PilcrowLeft as default };\n//# sourceMappingURL=pilcrow-left.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}