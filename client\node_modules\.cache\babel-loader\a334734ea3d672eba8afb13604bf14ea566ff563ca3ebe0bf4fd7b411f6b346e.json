{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"8\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"1\",\n  key: \"oynpb5\"\n}], [\"path\", {\n  d: \"M7 3v18\",\n  key: \"bbkbws\"\n}], [\"path\", {\n  d: \"M20.4 18.9c.2.5-.1 1.1-.6 1.3l-1.9.7c-.5.2-1.1-.1-1.3-.6L11.1 5.1c-.2-.5.1-1.1.6-1.3l1.9-.7c.5-.2 1.1.1 1.3.6Z\",\n  key: \"1qboyk\"\n}]];\nconst LibraryBig = createLucideIcon(\"library-big\", __iconNode);\nexport { __iconNode, LibraryBig as default };\n//# sourceMappingURL=library-big.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}