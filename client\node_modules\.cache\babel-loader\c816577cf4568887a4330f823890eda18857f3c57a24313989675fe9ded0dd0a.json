{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20.5 14.9A9 9 0 0 0 9.1 3.5\",\n  key: \"1iebmn\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M5.6 5.6C3 8.3 2.2 12.5 4 16l-2 6 6-2c3.4 1.8 7.6 1.1 10.3-1.7\",\n  key: \"1ov8ce\"\n}]];\nconst MessageCircleOff = createLucideIcon(\"message-circle-off\", __iconNode);\nexport { __iconNode, MessageCircleOff as default };\n//# sourceMappingURL=message-circle-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}