{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22v-5\",\n  key: \"1ega77\"\n}], [\"path\", {\n  d: \"M9 8V2\",\n  key: \"14iosj\"\n}], [\"path\", {\n  d: \"M15 8V2\",\n  key: \"18g5xt\"\n}], [\"path\", {\n  d: \"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z\",\n  key: \"osxo6l\"\n}]];\nconst Plug = createLucideIcon(\"plug\", __iconNode);\nexport { __iconNode, Plug as default };\n//# sourceMappingURL=plug.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}