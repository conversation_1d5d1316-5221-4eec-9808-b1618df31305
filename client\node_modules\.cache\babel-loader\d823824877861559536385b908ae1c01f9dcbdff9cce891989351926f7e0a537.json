{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 6L12 2L16 6\",\n  key: \"1yvkyx\"\n}], [\"path\", {\n  d: \"M12 2V22\",\n  key: \"r89rzk\"\n}]];\nconst MoveUp = createLucideIcon(\"move-up\", __iconNode);\nexport { __iconNode, MoveUp as default };\n//# sourceMappingURL=move-up.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}