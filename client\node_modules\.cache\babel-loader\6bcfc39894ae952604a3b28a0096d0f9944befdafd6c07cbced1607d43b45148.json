{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 20h10\",\n  key: \"e6iznv\"\n}], [\"path\", {\n  d: \"M10 20c5.5-2.5.8-6.4 3-10\",\n  key: \"161w41\"\n}], [\"path\", {\n  d: \"M9.5 9.4c1.1.8 1.8 2.2 2.3 3.7-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2 2.8-.5 4.4 0 5.5.8z\",\n  key: \"9gtqwd\"\n}], [\"path\", {\n  d: \"M14.1 6a7 7 0 0 0-1.1 4c1.9-.1 3.3-.6 4.3-1.4 1-1 1.6-2.3 1.7-4.6-2.7.1-4 1-4.9 2z\",\n  key: \"bkxnd2\"\n}]];\nconst Sprout = createLucideIcon(\"sprout\", __iconNode);\nexport { __iconNode, Sprout as default };\n//# sourceMappingURL=sprout.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}