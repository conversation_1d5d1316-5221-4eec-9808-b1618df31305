{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"5\",\n  y: \"2\",\n  width: \"14\",\n  height: \"20\",\n  rx: \"7\",\n  key: \"11ol66\"\n}], [\"path\", {\n  d: \"M12 6v4\",\n  key: \"16clxf\"\n}]];\nconst Mouse = createLucideIcon(\"mouse\", __iconNode);\nexport { __iconNode, Mouse as default };\n//# sourceMappingURL=mouse.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}