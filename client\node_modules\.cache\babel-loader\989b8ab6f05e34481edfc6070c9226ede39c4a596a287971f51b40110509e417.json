{"ast": null, "code": "import utils from './../utils.js';\nimport platform from '../platform/index.js';\nexport default platform.hasStandardBrowserEnv ?\n// Standard browser envs support document.cookie\n{\n  write(name, value, expires, path, domain, secure) {\n    const cookie = [name + '=' + encodeURIComponent(value)];\n    utils.isNumber(expires) && cookie.push('expires=' + new Date(expires).toGMTString());\n    utils.isString(path) && cookie.push('path=' + path);\n    utils.isString(domain) && cookie.push('domain=' + domain);\n    secure === true && cookie.push('secure');\n    document.cookie = cookie.join('; ');\n  },\n  read(name) {\n    const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n    return match ? decodeURIComponent(match[3]) : null;\n  },\n  remove(name) {\n    this.write(name, '', Date.now() - 86400000);\n  }\n} :\n// Non-standard browser env (web workers, react-native) lack needed support.\n{\n  write() {},\n  read() {\n    return null;\n  },\n  remove() {}\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}