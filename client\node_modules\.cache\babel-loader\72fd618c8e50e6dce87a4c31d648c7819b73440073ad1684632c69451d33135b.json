{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\",\n  key: \"1c8476\"\n}], [\"path\", {\n  d: \"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\",\n  key: \"1ydtos\"\n}], [\"path\", {\n  d: \"M7 3v4a1 1 0 0 0 1 1h7\",\n  key: \"t51u73\"\n}]];\nconst Save = createLucideIcon(\"save\", __iconNode);\nexport { __iconNode, Save as default };\n//# sourceMappingURL=save.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}