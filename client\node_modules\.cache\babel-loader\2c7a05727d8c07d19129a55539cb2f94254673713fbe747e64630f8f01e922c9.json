{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z\",\n  key: \"qazsjp\"\n}], [\"path\", {\n  d: \"M15 3v4a2 2 0 0 0 2 2h4\",\n  key: \"40519r\"\n}]];\nconst StickyNote = createLucideIcon(\"sticky-note\", __iconNode);\nexport { __iconNode, StickyNote as default };\n//# sourceMappingURL=sticky-note.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}