{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 2v17.5A2.5 2.5 0 0 1 6.5 22A2.5 2.5 0 0 1 4 19.5V2\",\n  key: \"1hjrqt\"\n}], [\"path\", {\n  d: \"M20 2v17.5a2.5 2.5 0 0 1-2.5 2.5a2.5 2.5 0 0 1-2.5-2.5V2\",\n  key: \"16lc8n\"\n}], [\"path\", {\n  d: \"M3 2h7\",\n  key: \"7s29d5\"\n}], [\"path\", {\n  d: \"M14 2h7\",\n  key: \"7sicin\"\n}], [\"path\", {\n  d: \"M9 16H4\",\n  key: \"1bfye3\"\n}], [\"path\", {\n  d: \"M20 16h-5\",\n  key: \"ddnjpe\"\n}]];\nconst TestTubes = createLucideIcon(\"test-tubes\", __iconNode);\nexport { __iconNode, TestTubes as default };\n//# sourceMappingURL=test-tubes.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}