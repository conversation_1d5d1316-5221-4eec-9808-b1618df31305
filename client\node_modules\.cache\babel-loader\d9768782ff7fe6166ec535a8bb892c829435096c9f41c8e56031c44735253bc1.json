{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"8\",\n  key: \"1gshiw\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"3\",\n  key: \"ilqhr7\"\n}], [\"path\", {\n  d: \"M7 22h10\",\n  key: \"10w4w3\"\n}], [\"path\", {\n  d: \"M12 22v-4\",\n  key: \"1utk9m\"\n}]];\nconst Webcam = createLucideIcon(\"webcam\", __iconNode);\nexport { __iconNode, Webcam as default };\n//# sourceMappingURL=webcam.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}