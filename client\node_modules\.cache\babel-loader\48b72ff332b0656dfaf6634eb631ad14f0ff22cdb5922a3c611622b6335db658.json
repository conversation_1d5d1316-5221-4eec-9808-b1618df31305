{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5.8 11.3 2 22l10.7-3.79\",\n  key: \"gwxi1d\"\n}], [\"path\", {\n  d: \"M4 3h.01\",\n  key: \"1vcuye\"\n}], [\"path\", {\n  d: \"M22 8h.01\",\n  key: \"1mrtc2\"\n}], [\"path\", {\n  d: \"M15 2h.01\",\n  key: \"1cjtqr\"\n}], [\"path\", {\n  d: \"M22 20h.01\",\n  key: \"1mrys2\"\n}], [\"path\", {\n  d: \"m22 2-2.24.75a2.9 2.9 0 0 0-1.96 3.12c.1.86-.57 1.63-1.45 1.63h-.38c-.86 0-1.6.6-1.76 1.44L14 10\",\n  key: \"hbicv8\"\n}], [\"path\", {\n  d: \"m22 13-.82-.33c-.86-.34-1.82.2-1.98 1.11c-.11.7-.72 1.22-1.43 1.22H17\",\n  key: \"1i94pl\"\n}], [\"path\", {\n  d: \"m11 2 .33.82c.34.86-.2 1.82-1.11 1.98C9.52 4.9 9 5.52 9 6.23V7\",\n  key: \"1cofks\"\n}], [\"path\", {\n  d: \"M11 13c1.93 1.93 2.83 4.17 2 5-.83.83-3.07-.07-5-2-1.93-1.93-2.83-4.17-2-5 .83-.83 3.07.07 5 2Z\",\n  key: \"4kbmks\"\n}]];\nconst PartyPopper = createLucideIcon(\"party-popper\", __iconNode);\nexport { __iconNode, PartyPopper as default };\n//# sourceMappingURL=party-popper.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}