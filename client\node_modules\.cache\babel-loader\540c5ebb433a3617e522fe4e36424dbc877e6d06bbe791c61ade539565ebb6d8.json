{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5\",\n  key: \"ftymec\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"14\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"158x01\"\n}]];\nconst Video = createLucideIcon(\"video\", __iconNode);\nexport { __iconNode, Video as default };\n//# sourceMappingURL=video.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}