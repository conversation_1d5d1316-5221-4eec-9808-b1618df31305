{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 10v2.2l1.6 1\",\n  key: \"n3r21l\"\n}], [\"path\", {\n  d: \"m16.13 7.66-.81-4.05a2 2 0 0 0-2-1.61h-2.68a2 2 0 0 0-2 1.61l-.78 4.05\",\n  key: \"18k57s\"\n}], [\"path\", {\n  d: \"m7.88 16.36.8 4a2 2 0 0 0 2 1.61h2.72a2 2 0 0 0 2-1.61l.81-4.05\",\n  key: \"16ny36\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"6\",\n  key: \"1vlfrh\"\n}]];\nconst Watch = createLucideIcon(\"watch\", __iconNode);\nexport { __iconNode, Watch as default };\n//# sourceMappingURL=watch.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}