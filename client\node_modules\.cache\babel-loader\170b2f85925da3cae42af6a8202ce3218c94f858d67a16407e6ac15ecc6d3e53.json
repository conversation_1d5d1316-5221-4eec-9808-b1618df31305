{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"18\",\n  r: \"4\",\n  key: \"m3r9ws\"\n}], [\"path\", {\n  d: \"M16 18V2\",\n  key: \"40x2m5\"\n}]];\nconst Music3 = createLucideIcon(\"music-3\", __iconNode);\nexport { __iconNode, Music3 as default };\n//# sourceMappingURL=music-3.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}