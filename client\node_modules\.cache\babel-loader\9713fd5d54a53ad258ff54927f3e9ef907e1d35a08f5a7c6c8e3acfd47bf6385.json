{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M13 8.129A4 4 0 0 1 15.873 11\",\n  key: \"my0cn3\"\n}], [\"path\", {\n  d: \"m19 5-1.256 1.256\",\n  key: \"1yg6a6\"\n}], [\"path\", {\n  d: \"M20 12h2\",\n  key: \"1q8mjw\"\n}], [\"path\", {\n  d: \"M9 8a5 5 0 1 0 7 7 7 7 0 1 1-7-7\",\n  key: \"4qob92\"\n}]];\nconst SunMoon = createLucideIcon(\"sun-moon\", __iconNode);\nexport { __iconNode, SunMoon as default };\n//# sourceMappingURL=sun-moon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}