{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 15h2\",\n  key: \"199qp6\"\n}], [\"path\", {\n  d: \"M12 12v3\",\n  key: \"158kv8\"\n}], [\"path\", {\n  d: \"M12 19v3\",\n  key: \"npa21l\"\n}], [\"path\", {\n  d: \"M15.282 19a1 1 0 0 0 .948-.68l2.37-6.988a7 7 0 1 0-13.2 0l2.37 6.988a1 1 0 0 0 .948.68z\",\n  key: \"1jofit\"\n}], [\"path\", {\n  d: \"M9 9a3 3 0 1 1 6 0\",\n  key: \"jdoeu8\"\n}]];\nconst ParkingMeter = createLucideIcon(\"parking-meter\", __iconNode);\nexport { __iconNode, ParkingMeter as default };\n//# sourceMappingURL=parking-meter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}