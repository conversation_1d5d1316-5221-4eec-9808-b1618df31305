{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z\",\n  key: \"1a8usu\"\n}], [\"path\", {\n  d: \"m15 5 4 4\",\n  key: \"1mk7zo\"\n}]];\nconst Pencil = createLucideIcon(\"pencil\", __iconNode);\nexport { __iconNode, Pencil as default };\n//# sourceMappingURL=pencil.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}