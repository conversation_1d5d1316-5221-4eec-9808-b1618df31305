{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12 17-5-5 5-5\",\n  key: \"1s3y5u\"\n}], [\"path\", {\n  d: \"M22 18v-2a4 4 0 0 0-4-4H7\",\n  key: \"1fcyog\"\n}], [\"path\", {\n  d: \"m7 17-5-5 5-5\",\n  key: \"1ed8i2\"\n}]];\nconst ReplyAll = createLucideIcon(\"reply-all\", __iconNode);\nexport { __iconNode, ReplyAll as default };\n//# sourceMappingURL=reply-all.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}