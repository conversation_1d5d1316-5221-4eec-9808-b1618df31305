{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 8c2-3-2-3 0-6\",\n  key: \"1ldv5m\"\n}], [\"path\", {\n  d: \"M15.5 8c2-3-2-3 0-6\",\n  key: \"1otqoz\"\n}], [\"path\", {\n  d: \"M6 10h.01\",\n  key: \"1lbq93\"\n}], [\"path\", {\n  d: \"M6 14h.01\",\n  key: \"zudwn7\"\n}], [\"path\", {\n  d: \"M10 16v-4\",\n  key: \"1c25yv\"\n}], [\"path\", {\n  d: \"M14 16v-4\",\n  key: \"1dkbt8\"\n}], [\"path\", {\n  d: \"M18 16v-4\",\n  key: \"1yg9me\"\n}], [\"path\", {\n  d: \"M20 6a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3\",\n  key: \"1ubg90\"\n}], [\"path\", {\n  d: \"M5 20v2\",\n  key: \"1abpe8\"\n}], [\"path\", {\n  d: \"M19 20v2\",\n  key: \"kqn6ft\"\n}]];\nconst Heater = createLucideIcon(\"heater\", __iconNode);\nexport { __iconNode, Heater as default };\n//# sourceMappingURL=heater.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}