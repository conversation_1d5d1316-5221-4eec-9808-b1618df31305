{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"3 11 22 2 13 21 11 13 3 11\",\n  key: \"1ltx0t\"\n}]];\nconst Navigation = createLucideIcon(\"navigation\", __iconNode);\nexport { __iconNode, Navigation as default };\n//# sourceMappingURL=navigation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}