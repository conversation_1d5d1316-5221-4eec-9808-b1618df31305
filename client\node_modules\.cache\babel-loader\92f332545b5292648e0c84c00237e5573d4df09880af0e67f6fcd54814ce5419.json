{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polyline\", {\n  points: \"14.5 17.5 3 6 3 3 6 3 17.5 14.5\",\n  key: \"1hfsw2\"\n}], [\"line\", {\n  x1: \"13\",\n  x2: \"19\",\n  y1: \"19\",\n  y2: \"13\",\n  key: \"1vrmhu\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"20\",\n  y1: \"16\",\n  y2: \"20\",\n  key: \"1bron3\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"21\",\n  y1: \"21\",\n  y2: \"19\",\n  key: \"13pww6\"\n}], [\"polyline\", {\n  points: \"14.5 6.5 18 3 21 3 21 6 17.5 9.5\",\n  key: \"hbey2j\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"9\",\n  y1: \"14\",\n  y2: \"18\",\n  key: \"1hf58s\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"4\",\n  y1: \"17\",\n  y2: \"20\",\n  key: \"pidxm4\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"5\",\n  y1: \"19\",\n  y2: \"21\",\n  key: \"1pehsh\"\n}]];\nconst Swords = createLucideIcon(\"swords\", __iconNode);\nexport { __iconNode, Swords as default };\n//# sourceMappingURL=swords.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}