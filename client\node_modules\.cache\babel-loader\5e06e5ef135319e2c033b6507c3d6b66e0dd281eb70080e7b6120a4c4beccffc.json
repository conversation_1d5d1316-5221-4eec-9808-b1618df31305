{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 19H5v-6\",\n  key: \"8awifj\"\n}], [\"path\", {\n  d: \"M13 5h6v6\",\n  key: \"7voy1q\"\n}], [\"path\", {\n  d: \"M19 5 5 19\",\n  key: \"wwaj1z\"\n}]];\nconst MoveDiagonal = createLucideIcon(\"move-diagonal\", __iconNode);\nexport { __iconNode, MoveDiagonal as default };\n//# sourceMappingURL=move-diagonal.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}